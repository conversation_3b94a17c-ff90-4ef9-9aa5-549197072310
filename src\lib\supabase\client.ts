import { createBrowserClient } from '@supabase/ssr'

export function createClient() {
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
  const hostname = new URL(siteUrl).hostname

  // Fix localhost cookie domain issue
  const cookieDomain = hostname === 'localhost' ? undefined : hostname

  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookieOptions: {
        domain: cookieDomain,
        sameSite: 'lax',
        secure: hostname !== 'localhost'
      },
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true
      }
    }
  )
}
