{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vIkrLhuFejbE7f2q9zDPJdA0q6VuXpH+EB9OFkD5UQc=", "__NEXT_PREVIEW_MODE_ID": "e59cd7ec170b7889fca6c53a6a28721d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "231147d609aa73d290575498fcb1b108dce7f7491ae0f8a4c60f66d5fda83611", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "394dd2d4c1cc184ce771606c8e281241d4156cf6b21d1f762166631b5f075169"}}}, "instrumentation": null, "functions": {}}