{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vIkrLhuFejbE7f2q9zDPJdA0q6VuXpH+EB9OFkD5UQc=", "__NEXT_PREVIEW_MODE_ID": "fdf1688c1f710211a0ad2e20259c9a4a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f5d07166a5698900b9a6b786a403f9f699cd43851e9d619b396108179f23a173", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "dbdf0ed88899ebd816d297fdaf794e9be7551d30dae06a385c63a9612cd59ee8"}}}, "sortedMiddleware": ["/"], "functions": {}}