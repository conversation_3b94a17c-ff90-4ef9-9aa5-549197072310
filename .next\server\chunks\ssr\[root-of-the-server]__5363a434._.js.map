{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n/**\n * Calculate estimated reading time for a text\n * @param text - The text content to analyze\n * @param wordsPerMinute - Average reading speed (default: 200 words per minute)\n * @returns Estimated reading time in minutes\n */\nexport function calculateReadingTime(text: string, wordsPerMinute: number = 200): number {\n  // Remove markdown syntax and HTML tags for more accurate word count\n  const cleanText = text\n    .replace(/```[\\s\\S]*?```/g, '') // Remove code blocks\n    .replace(/`[^`]*`/g, '') // Remove inline code\n    .replace(/#{1,6}\\s/g, '') // Remove markdown headers\n    .replace(/\\*\\*([^*]+)\\*\\*/g, '$1') // Remove bold markdown\n    .replace(/\\*([^*]+)\\*/g, '$1') // Remove italic markdown\n    .replace(/\\[([^\\]]+)\\]\\([^)]+\\)/g, '$1') // Remove links, keep text\n    .replace(/<[^>]*>/g, '') // Remove HTML tags\n    .replace(/\\s+/g, ' ') // Normalize whitespace\n    .trim()\n\n  const wordCount = cleanText.split(' ').filter(word => word.length > 0).length\n  const readingTime = Math.ceil(wordCount / wordsPerMinute)\n  \n  return Math.max(1, readingTime) // Minimum 1 minute\n}\n\n/**\n * Create an excerpt from text content\n * @param text - The full text content\n * @param maxLength - Maximum length of the excerpt (default: 200)\n * @returns Truncated excerpt with ellipsis if needed\n */\nexport function createExcerpt(text: string, maxLength: number = 200): string {\n  // Remove markdown syntax for cleaner excerpt\n  const cleanText = text\n    .replace(/```[\\s\\S]*?```/g, '') // Remove code blocks\n    .replace(/`[^`]*`/g, '') // Remove inline code\n    .replace(/#{1,6}\\s/g, '') // Remove markdown headers\n    .replace(/\\*\\*([^*]+)\\*\\*/g, '$1') // Remove bold markdown\n    .replace(/\\*([^*]+)\\*/g, '$1') // Remove italic markdown\n    .replace(/\\[([^\\]]+)\\]\\([^)]+\\)/g, '$1') // Remove links, keep text\n    .replace(/<[^>]*>/g, '') // Remove HTML tags\n    .replace(/\\s+/g, ' ') // Normalize whitespace\n    .trim()\n\n  if (cleanText.length <= maxLength) {\n    return cleanText\n  }\n\n  // Find the last complete word within the limit\n  const truncated = cleanText.substring(0, maxLength)\n  const lastSpaceIndex = truncated.lastIndexOf(' ')\n  \n  if (lastSpaceIndex > 0) {\n    return truncated.substring(0, lastSpaceIndex) + '...'\n  }\n  \n  return truncated + '...'\n}\n\n/**\n * Search through posts by title and content\n * @param posts - Array of posts to search through\n * @param query - Search query string\n * @returns Filtered array of posts matching the query\n */\nexport function searchPosts<T extends { title: string; content: string }>(\n  posts: T[],\n  query: string\n): T[] {\n  if (!query.trim()) {\n    return posts\n  }\n\n  const searchTerm = query.toLowerCase().trim()\n  \n  return posts.filter(post => {\n    const titleMatch = post.title.toLowerCase().includes(searchTerm)\n    const contentMatch = post.content.toLowerCase().includes(searchTerm)\n    return titleMatch || contentMatch\n  })\n}\n\n/**\n * Paginate an array of items\n * @param items - Array of items to paginate\n * @param page - Current page number (1-based)\n * @param itemsPerPage - Number of items per page\n * @returns Object with paginated items and pagination info\n */\nexport function paginateItems<T>(\n  items: T[],\n  page: number,\n  itemsPerPage: number\n): {\n  items: T[]\n  totalItems: number\n  totalPages: number\n  currentPage: number\n  hasNextPage: boolean\n  hasPreviousPage: boolean\n} {\n  const totalItems = items.length\n  const totalPages = Math.ceil(totalItems / itemsPerPage)\n  const currentPage = Math.max(1, Math.min(page, totalPages))\n  const startIndex = (currentPage - 1) * itemsPerPage\n  const endIndex = startIndex + itemsPerPage\n  \n  return {\n    items: items.slice(startIndex, endIndex),\n    totalItems,\n    totalPages,\n    currentPage,\n    hasNextPage: currentPage < totalPages,\n    hasPreviousPage: currentPage > 1\n  }\n}\n\n/**\n * Format a date for display\n * @param date - Date string or Date object\n * @param options - Intl.DateTimeFormat options\n * @returns Formatted date string\n */\nexport function formatDate(\n  date: string | Date,\n  options: Intl.DateTimeFormatOptions = {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  }\n): string {\n  return new Date(date).toLocaleDateString('en-US', options)\n}\n\n/**\n * Get relative time string (e.g., \"2 days ago\")\n * @param date - Date string or Date object\n * @returns Relative time string\n */\nexport function getRelativeTime(date: string | Date): string {\n  const now = new Date()\n  const targetDate = new Date(date)\n  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000)\n\n  if (diffInSeconds < 60) {\n    return 'just now'\n  }\n\n  const diffInMinutes = Math.floor(diffInSeconds / 60)\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`\n  }\n\n  const diffInHours = Math.floor(diffInMinutes / 60)\n  if (diffInHours < 24) {\n    return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`\n  }\n\n  const diffInDays = Math.floor(diffInHours / 24)\n  if (diffInDays < 7) {\n    return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`\n  }\n\n  const diffInWeeks = Math.floor(diffInDays / 7)\n  if (diffInWeeks < 4) {\n    return `${diffInWeeks} week${diffInWeeks === 1 ? '' : 's'} ago`\n  }\n\n  const diffInMonths = Math.floor(diffInDays / 30)\n  if (diffInMonths < 12) {\n    return `${diffInMonths} month${diffInMonths === 1 ? '' : 's'} ago`\n  }\n\n  const diffInYears = Math.floor(diffInDays / 365)\n  return `${diffInYears} year${diffInYears === 1 ? '' : 's'} ago`\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAQO,SAAS,qBAAqB,IAAY,EAAE,iBAAyB,GAAG;IAC7E,oEAAoE;IACpE,MAAM,YAAY,KACf,OAAO,CAAC,mBAAmB,IAAI,qBAAqB;KACpD,OAAO,CAAC,YAAY,IAAI,qBAAqB;KAC7C,OAAO,CAAC,aAAa,IAAI,0BAA0B;KACnD,OAAO,CAAC,oBAAoB,MAAM,uBAAuB;KACzD,OAAO,CAAC,gBAAgB,MAAM,yBAAyB;KACvD,OAAO,CAAC,0BAA0B,MAAM,0BAA0B;KAClE,OAAO,CAAC,YAAY,IAAI,mBAAmB;KAC3C,OAAO,CAAC,QAAQ,KAAK,uBAAuB;KAC5C,IAAI;IAEP,MAAM,YAAY,UAAU,KAAK,CAAC,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAAG,MAAM;IAC7E,MAAM,cAAc,KAAK,IAAI,CAAC,YAAY;IAE1C,OAAO,KAAK,GAAG,CAAC,GAAG,aAAa,mBAAmB;;AACrD;AAQO,SAAS,cAAc,IAAY,EAAE,YAAoB,GAAG;IACjE,6CAA6C;IAC7C,MAAM,YAAY,KACf,OAAO,CAAC,mBAAmB,IAAI,qBAAqB;KACpD,OAAO,CAAC,YAAY,IAAI,qBAAqB;KAC7C,OAAO,CAAC,aAAa,IAAI,0BAA0B;KACnD,OAAO,CAAC,oBAAoB,MAAM,uBAAuB;KACzD,OAAO,CAAC,gBAAgB,MAAM,yBAAyB;KACvD,OAAO,CAAC,0BAA0B,MAAM,0BAA0B;KAClE,OAAO,CAAC,YAAY,IAAI,mBAAmB;KAC3C,OAAO,CAAC,QAAQ,KAAK,uBAAuB;KAC5C,IAAI;IAEP,IAAI,UAAU,MAAM,IAAI,WAAW;QACjC,OAAO;IACT;IAEA,+CAA+C;IAC/C,MAAM,YAAY,UAAU,SAAS,CAAC,GAAG;IACzC,MAAM,iBAAiB,UAAU,WAAW,CAAC;IAE7C,IAAI,iBAAiB,GAAG;QACtB,OAAO,UAAU,SAAS,CAAC,GAAG,kBAAkB;IAClD;IAEA,OAAO,YAAY;AACrB;AAQO,SAAS,YACd,KAAU,EACV,KAAa;IAEb,IAAI,CAAC,MAAM,IAAI,IAAI;QACjB,OAAO;IACT;IAEA,MAAM,aAAa,MAAM,WAAW,GAAG,IAAI;IAE3C,OAAO,MAAM,MAAM,CAAC,CAAA;QAClB,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC;QACrD,MAAM,eAAe,KAAK,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC;QACzD,OAAO,cAAc;IACvB;AACF;AASO,SAAS,cACd,KAAU,EACV,IAAY,EACZ,YAAoB;IASpB,MAAM,aAAa,MAAM,MAAM;IAC/B,MAAM,aAAa,KAAK,IAAI,CAAC,aAAa;IAC1C,MAAM,cAAc,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM;IAC/C,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI;IACvC,MAAM,WAAW,aAAa;IAE9B,OAAO;QACL,OAAO,MAAM,KAAK,CAAC,YAAY;QAC/B;QACA;QACA;QACA,aAAa,cAAc;QAC3B,iBAAiB,cAAc;IACjC;AACF;AAQO,SAAS,WACd,IAAmB,EACnB,UAAsC;IACpC,MAAM;IACN,OAAO;IACP,KAAK;AACP,CAAC;IAED,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;AACpD;AAOO,SAAS,gBAAgB,IAAmB;IACjD,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,IAAI,KAAK;IAC5B,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,WAAW,OAAO,EAAE,IAAI;IAE1E,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,GAAG,cAAc,OAAO,EAAE,kBAAkB,IAAI,KAAK,IAAI,IAAI,CAAC;IACvE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,GAAG,YAAY,KAAK,EAAE,gBAAgB,IAAI,KAAK,IAAI,IAAI,CAAC;IACjE;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,GAAG,WAAW,IAAI,EAAE,eAAe,IAAI,KAAK,IAAI,IAAI,CAAC;IAC9D;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,IAAI,cAAc,GAAG;QACnB,OAAO,GAAG,YAAY,KAAK,EAAE,gBAAgB,IAAI,KAAK,IAAI,IAAI,CAAC;IACjE;IAEA,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa;IAC7C,IAAI,eAAe,IAAI;QACrB,OAAO,GAAG,aAAa,MAAM,EAAE,iBAAiB,IAAI,KAAK,IAAI,IAAI,CAAC;IACpE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,OAAO,GAAG,YAAY,KAAK,EAAE,gBAAgB,IAAI,KAAK,IAAI,IAAI,CAAC;AACjE", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/post-card.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport { Post } from '@/types/database'\nimport { calculateReadingTime, createExcerpt, formatDate, cn } from '@/lib/utils'\n\ninterface PostCardProps {\n  post: Post\n  featured?: boolean\n  className?: string\n  showExcerpt?: boolean\n  excerptLength?: number\n  style?: React.CSSProperties\n}\n\nexport function PostCard({ \n  post, \n  featured = false, \n  className,\n  showExcerpt = true,\n  excerptLength = 200\n}: PostCardProps) {\n  const readingTime = calculateReadingTime(post.content)\n  const excerpt = showExcerpt ? createExcerpt(post.content, excerptLength) : ''\n  const isRecent = new Date(post.created_at) > new Date(Date.now() - 1 * 24 * 60 * 60 * 1000) // Within last 1 day\n\n  if (featured) {\n    return (\n      <article className={cn(\n        \"group relative overflow-hidden\",\n        \"bg-gradient-to-br from-primary/5 via-primary/3 to-background\",\n        \"rounded-2xl border border-primary/20 shadow-lg\",\n        \"hover:shadow-xl hover:border-primary/30\",\n        \"transition-all duration-300\",\n        \"p-6 lg:p-8\",\n        className\n      )}>\n        {/* Featured Badge */}\n        <div className=\"absolute top-4 right-4\" suppressHydrationWarning>\n          <span className=\"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary text-primary-foreground shadow-sm\">\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n            </svg>\n          </span>\n        </div>\n\n        <div className=\"space-y-4\" suppressHydrationWarning>\n          <div suppressHydrationWarning>\n            <h2 className=\"text-2xl lg:text-3xl font-bold text-foreground mb-3 group-hover:text-primary transition-colors leading-tight\">\n              <Link href={`/posts/${post.id}`} className=\"block\">\n                {post.title}\n              </Link>\n            </h2>\n            \n            {showExcerpt && (\n              <p className=\"text-muted-foreground leading-relaxed text-base lg:text-lg\">\n                {excerpt}\n              </p>\n            )}\n          </div>\n\n          <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 pt-2\" suppressHydrationWarning>\n            <div className=\"flex items-center gap-4 text-sm text-muted-foreground\" suppressHydrationWarning>\n              <time className=\"flex items-center gap-1\">\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                </svg>\n                {formatDate(post.created_at)}\n              </time>\n              \n              <span className=\"flex items-center gap-1\">\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n                {readingTime} min read\n              </span>\n            </div>\n\n            <Link\n              href={`/posts/${post.id}`}\n              className=\"inline-flex items-center text-primary hover:text-primary/80 font-medium transition-all duration-200 group/link text-sm\"\n            >\n              Read full article\n              <svg className=\"w-4 h-4 ml-1 group-hover/link:translate-x-1 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n              </svg>\n            </Link>\n          </div>\n        </div>\n      </article>\n    )\n  }\n\n  return (\n    <article className={cn(\n      \"group relative overflow-hidden\",\n      \"bg-card rounded-xl shadow-sm border border-border\",\n      \"hover:shadow-xl hover:border-primary/30 hover:-translate-y-1\",\n      \"transition-all duration-300 ease-out\",\n      \"p-6\",\n      className\n    )}>\n      {/* New Post Indicator */}\n      {isRecent && (\n        <div className=\"absolute top-6 right-6 z-10\" suppressHydrationWarning>\n          <span className=\"inline-flex items-center px-3.5 py-1.5 rounded-full text-xs font-bold bg-gradient-to-br from-green-400 to-emerald-600 text-white shadow-xl transform hover:scale-105 transition-all duration-300 ease-out\">\n            <svg className=\"w-3 h-3 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n            </svg>\n            New\n          </span>\n        </div>\n      )}\n\n      {/* Gradient overlay on hover */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl\" suppressHydrationWarning />\n\n      <div className=\"relative space-y-4\" suppressHydrationWarning>\n        <div suppressHydrationWarning>\n          <h3 className=\"text-xl font-semibold text-card-foreground mb-3 group-hover:text-primary transition-colors leading-tight line-clamp-2 pr-20\">\n            <Link href={`/posts/${post.id}`} className=\"block\">\n              {post.title}\n            </Link>\n          </h3>\n\n          {showExcerpt && (\n            <p className=\"text-muted-foreground leading-relaxed line-clamp-3 text-sm\">\n              {excerpt}\n            </p>\n          )}\n        </div>\n\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 pt-4 border-t border-border/50\" suppressHydrationWarning>\n          <div className=\"flex items-center gap-4\" suppressHydrationWarning>\n            <time className=\"flex items-center gap-2 text-sm text-muted-foreground\" title={formatDate(post.created_at)}>\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n              </svg>\n              {formatDate(post.created_at)}\n            </time>\n\n            <span className=\"inline-flex items-center gap-1 px-2 py-1 rounded-md bg-primary/10 text-primary text-xs font-medium\">\n              <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n              {readingTime}\n            </span>\n          </div>\n\n          <Link\n            href={`/posts/${post.id}`}\n            className=\"group inline-flex items-center gap-2 text-sm font-medium text-primary hover:text-primary/80 transition-all duration-200\"\n          >\n            Read more\n            <svg className=\"w-4 h-4 group-hover:translate-x-1 transition-transform duration-200\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n            </svg>\n          </Link>\n        </div>\n      </div>\n    </article>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAWO,SAAS,SAAS,EACvB,IAAI,EACJ,WAAW,KAAK,EAChB,SAAS,EACT,cAAc,IAAI,EAClB,gBAAgB,GAAG,EACL;IACd,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK,OAAO;IACrD,MAAM,UAAU,cAAc,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,OAAO,EAAE,iBAAiB;IAC3E,MAAM,WAAW,IAAI,KAAK,KAAK,UAAU,IAAI,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,oBAAoB;;IAEhH,IAAI,UAAU;QACZ,qBACE,8OAAC;YAAQ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACnB,kCACA,gEACA,kDACA,2CACA,+BACA,cACA;;8BAGA,8OAAC;oBAAI,WAAU;oBAAyB,wBAAwB;8BAC9D,cAAA,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;8BAK3E,8OAAC;oBAAI,WAAU;oBAAY,wBAAwB;;sCACjD,8OAAC;4BAAI,wBAAwB;;8CAC3B,8OAAC;oCAAG,WAAU;8CACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;wCAAE,WAAU;kDACxC,KAAK,KAAK;;;;;;;;;;;gCAId,6BACC,8OAAC;oCAAE,WAAU;8CACV;;;;;;;;;;;;sCAKP,8OAAC;4BAAI,WAAU;4BAA0E,wBAAwB;;8CAC/G,8OAAC;oCAAI,WAAU;oCAAwD,wBAAwB;;sDAC7F,8OAAC;4CAAK,WAAU;;8DACd,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;gDAEtE,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAAU;;;;;;;sDAG7B,8OAAC;4CAAK,WAAU;;8DACd,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;gDAEtE;gDAAY;;;;;;;;;;;;;8CAIjB,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;oCACzB,WAAU;;wCACX;sDAEC,8OAAC;4CAAI,WAAU;4CAAmE,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC1H,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOnF;IAEA,qBACE,8OAAC;QAAQ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACnB,kCACA,qDACA,gEACA,wCACA,OACA;;YAGC,0BACC,8OAAC;gBAAI,WAAU;gBAA8B,wBAAwB;0BACnE,cAAA,8OAAC;oBAAK,WAAU;;sCACd,8OAAC;4BAAI,WAAU;4BAAe,MAAK;4BAAe,SAAQ;sCACxD,cAAA,8OAAC;gCAAK,UAAS;gCAAU,GAAE;gCAAwI,UAAS;;;;;;;;;;;wBACxK;;;;;;;;;;;;0BAOZ,8OAAC;gBAAI,WAAU;gBAAgJ,wBAAwB;;;;;;0BAEvL,8OAAC;gBAAI,WAAU;gBAAqB,wBAAwB;;kCAC1D,8OAAC;wBAAI,wBAAwB;;0CAC3B,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;oCAAE,WAAU;8CACxC,KAAK,KAAK;;;;;;;;;;;4BAId,6BACC,8OAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;kCAKP,8OAAC;wBAAI,WAAU;wBAAoG,wBAAwB;;0CACzI,8OAAC;gCAAI,WAAU;gCAA0B,wBAAwB;;kDAC/D,8OAAC;wCAAK,WAAU;wCAAwD,OAAO,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAAU;;0DACvG,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CAEtE,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAAU;;;;;;;kDAG7B,8OAAC;wCAAK,WAAU;;0DACd,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CAEtE;;;;;;;;;;;;;0CAIL,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;gCACzB,WAAU;;oCACX;kDAEC,8OAAC;wCAAI,WAAU;wCAAsE,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC7H,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnF", "debugId": null}}, {"offset": {"line": 584, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/search-bar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface SearchBarProps {\n  onSearch: (query: string) => void\n  placeholder?: string\n  className?: string\n  autoFocus?: boolean\n}\n\nexport function SearchBar({ \n  onSearch, \n  placeholder = \"Search posts...\", \n  className,\n  autoFocus = false \n}: SearchBarProps) {\n  const [query, setQuery] = useState('')\n  const [isFocused, setIsFocused] = useState(false)\n  const inputRef = useRef<HTMLInputElement>(null)\n\n  useEffect(() => {\n    if (autoFocus && inputRef.current) {\n      inputRef.current.focus()\n    }\n  }, [autoFocus])\n\n  useEffect(() => {\n    // Debounce search to avoid too many calls\n    const timeoutId = setTimeout(() => {\n      onSearch(query)\n    }, 300)\n\n    return () => clearTimeout(timeoutId)\n  }, [query, onSearch])\n\n  const handleClear = () => {\n    setQuery('')\n    if (inputRef.current) {\n      inputRef.current.focus()\n    }\n  }\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      handleClear()\n    }\n  }\n\n  return (\n    <div className={cn(\n      \"relative group\",\n      className\n    )} suppressHydrationWarning>\n      <div className={cn(\n        \"relative flex items-center transition-all duration-200\",\n        \"bg-background border border-border rounded-lg\",\n        \"hover:border-primary/30 focus-within:border-primary/50\",\n        \"shadow-sm hover:shadow-md focus-within:shadow-md\",\n        isFocused && \"ring-2 ring-primary/20\"\n      )} suppressHydrationWarning>\n        {/* Search Icon */}\n        <div className=\"absolute left-3 flex items-center pointer-events-none\" suppressHydrationWarning>\n          <svg \n            className={cn(\n              \"w-4 h-4 transition-colors duration-200\",\n              isFocused || query ? \"text-primary\" : \"text-muted-foreground\"\n            )} \n            fill=\"none\" \n            stroke=\"currentColor\" \n            viewBox=\"0 0 24 24\"\n          >\n            <path \n              strokeLinecap=\"round\" \n              strokeLinejoin=\"round\" \n              strokeWidth={2} \n              d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" \n            />\n          </svg>\n        </div>\n\n        {/* Input Field */}\n        <input\n          ref={inputRef}\n          type=\"text\"\n          value={query}\n          onChange={(e) => setQuery(e.target.value)}\n          onFocus={() => setIsFocused(true)}\n          onBlur={() => setIsFocused(false)}\n          onKeyDown={handleKeyDown}\n          placeholder={placeholder}\n          className={cn(\n            \"w-full pl-10 pr-10 py-2.5 sm:py-3\",\n            \"bg-transparent border-0 outline-none\",\n            \"text-foreground placeholder:text-muted-foreground\",\n            \"text-sm sm:text-base\"\n          )}\n        />\n\n        {/* Clear Button */}\n        {query && (\n          <button\n            onClick={handleClear}\n            className={cn(\n              \"absolute right-3 flex items-center justify-center\",\n              \"w-5 h-5 rounded-full\",\n              \"text-muted-foreground hover:text-foreground\",\n              \"hover:bg-muted transition-all duration-200\",\n              \"focus:outline-none focus:ring-2 focus:ring-primary/20\"\n            )}\n            aria-label=\"Clear search\"\n          >\n            <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        )}\n      </div>\n\n      {/* Search Results Indicator */}\n      {query && (\n        <div className=\"absolute top-full left-0 right-0 mt-1\">\n          <div className=\"text-xs text-muted-foreground px-3 py-1\">\n            Searching for &quot;{query}&quot;...\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n\ninterface SearchResultsProps {\n  query: string\n  totalResults: number\n  className?: string\n}\n\nexport function SearchResults({ query, totalResults, className }: SearchResultsProps) {\n  if (!query) return null\n\n  return (\n    <div className={cn(\n      \"flex items-center justify-between\",\n      \"px-4 py-2 mb-6\",\n      \"bg-muted/50 rounded-lg border border-border\",\n      className\n    )}>\n      <div className=\"flex items-center gap-2\">\n        <svg className=\"w-4 h-4 text-muted-foreground\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n        </svg>\n        <span className=\"text-sm text-foreground\">\n          {totalResults === 0 ? (\n            <>No results found for <strong>&quot;{query}&quot;</strong></>\n          ) : (\n            <>\n              {totalResults} result{totalResults === 1 ? '' : 's'} for <strong>&quot;{query}&quot;</strong>\n            </>\n          )}\n        </span>\n      </div>\n      \n      {totalResults === 0 && (\n        <div className=\"text-xs text-muted-foreground\">\n          Try different keywords\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAYO,SAAS,UAAU,EACxB,QAAQ,EACR,cAAc,iBAAiB,EAC/B,SAAS,EACT,YAAY,KAAK,EACF;IACf,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,SAAS,OAAO,EAAE;YACjC,SAAS,OAAO,CAAC,KAAK;QACxB;IACF,GAAG;QAAC;KAAU;IAEd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0CAA0C;QAC1C,MAAM,YAAY,WAAW;YAC3B,SAAS;QACX,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAO;KAAS;IAEpB,MAAM,cAAc;QAClB,SAAS;QACT,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,KAAK;QACxB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,kBACA;QACC,wBAAwB;;0BACzB,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,0DACA,iDACA,0DACA,oDACA,aAAa;gBACZ,wBAAwB;;kCAEzB,8OAAC;wBAAI,WAAU;wBAAwD,wBAAwB;kCAC7F,cAAA,8OAAC;4BACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0CACA,aAAa,QAAQ,iBAAiB;4BAExC,MAAK;4BACL,QAAO;4BACP,SAAQ;sCAER,cAAA,8OAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,aAAa;gCACb,GAAE;;;;;;;;;;;;;;;;kCAMR,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,SAAS,IAAM,aAAa;wBAC5B,QAAQ,IAAM,aAAa;wBAC3B,WAAW;wBACX,aAAa;wBACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qCACA,wCACA,qDACA;;;;;;oBAKH,uBACC,8OAAC;wBACC,SAAS;wBACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA,wBACA,+CACA,8CACA;wBAEF,cAAW;kCAEX,cAAA,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;YAO5E,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBAA0C;wBAClC;wBAAM;;;;;;;;;;;;;;;;;;AAMvC;AAQO,SAAS,cAAc,EAAE,KAAK,EAAE,YAAY,EAAE,SAAS,EAAsB;IAClF,IAAI,CAAC,OAAO,OAAO;IAEnB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,qCACA,kBACA,+CACA;;0BAEA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;wBAAgC,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACvF,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;kCAEvE,8OAAC;wBAAK,WAAU;kCACb,iBAAiB,kBAChB;;gCAAE;8CAAqB,8OAAC;;wCAAO;wCAAO;wCAAM;;;;;;;;yDAE5C;;gCACG;gCAAa;gCAAQ,iBAAiB,IAAI,KAAK;gCAAI;8CAAK,8OAAC;;wCAAO;wCAAO;wCAAM;;;;;;;;;;;;;;;;;;;;YAMrF,iBAAiB,mBAChB,8OAAC;gBAAI,WAAU;0BAAgC;;;;;;;;;;;;AAMvD", "debugId": null}}, {"offset": {"line": 835, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/pagination.tsx"], "sourcesContent": ["'use client'\n\nimport { cn } from '@/lib/utils'\n\ninterface PaginationProps {\n  currentPage: number\n  totalPages: number\n  onPageChange: (page: number) => void\n  className?: string\n  showFirstLast?: boolean\n  maxVisiblePages?: number\n}\n\nexport function Pagination({\n  currentPage,\n  totalPages,\n  onPageChange,\n  className,\n  showFirstLast = true,\n  maxVisiblePages = 5\n}: PaginationProps) {\n  if (totalPages <= 1) return null\n\n  const getVisiblePages = () => {\n    const pages: (number | string)[] = []\n    \n    if (totalPages <= maxVisiblePages) {\n      // Show all pages if total is less than max visible\n      for (let i = 1; i <= totalPages; i++) {\n        pages.push(i)\n      }\n    } else {\n      // Calculate start and end of visible range\n      let start = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2))\n      const end = Math.min(totalPages, start + maxVisiblePages - 1)\n      \n      // Adjust start if we're near the end\n      if (end - start + 1 < maxVisiblePages) {\n        start = Math.max(1, end - maxVisiblePages + 1)\n      }\n      \n      // Add first page and ellipsis if needed\n      if (start > 1) {\n        pages.push(1)\n        if (start > 2) {\n          pages.push('...')\n        }\n      }\n      \n      // Add visible pages\n      for (let i = start; i <= end; i++) {\n        pages.push(i)\n      }\n      \n      // Add ellipsis and last page if needed\n      if (end < totalPages) {\n        if (end < totalPages - 1) {\n          pages.push('...')\n        }\n        pages.push(totalPages)\n      }\n    }\n    \n    return pages\n  }\n\n  const visiblePages = getVisiblePages()\n\n  const buttonClass = (isActive: boolean = false, isDisabled: boolean = false) =>\n    cn(\n      \"flex items-center justify-center\",\n      \"min-w-[40px] h-10 px-3\",\n      \"text-sm font-medium\",\n      \"border border-border rounded-lg\",\n      \"transition-all duration-200\",\n      \"focus:outline-none focus:ring-2 focus:ring-primary/20\",\n      isActive\n        ? \"bg-primary text-primary-foreground border-primary shadow-sm\"\n        : isDisabled\n        ? \"bg-muted text-muted-foreground cursor-not-allowed\"\n        : \"bg-background text-foreground hover:bg-muted hover:border-primary/30 hover:shadow-sm\"\n    )\n\n  return (\n    <nav \n      className={cn(\"flex items-center justify-center gap-1 sm:gap-2\", className)}\n      aria-label=\"Pagination\"\n    >\n      {/* First Page Button */}\n      {showFirstLast && currentPage > 1 && (\n        <button\n          onClick={() => onPageChange(1)}\n          className={buttonClass(false, false)}\n          aria-label=\"Go to first page\"\n        >\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 19l-7-7 7-7m8 14l-7-7 7-7\" />\n          </svg>\n        </button>\n      )}\n\n      {/* Previous Page Button */}\n      <button\n        onClick={() => onPageChange(currentPage - 1)}\n        disabled={currentPage <= 1}\n        className={buttonClass(false, currentPage <= 1)}\n        aria-label=\"Go to previous page\"\n      >\n        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n        </svg>\n        <span className=\"hidden sm:inline ml-1\">Previous</span>\n      </button>\n\n      {/* Page Numbers */}\n      <div className=\"flex items-center gap-1\">\n        {visiblePages.map((page, index) => (\n          <div key={index}>\n            {page === '...' ? (\n              <span className=\"flex items-center justify-center min-w-[40px] h-10 text-muted-foreground\">\n                ...\n              </span>\n            ) : (\n              <button\n                onClick={() => onPageChange(page as number)}\n                className={buttonClass(page === currentPage, false)}\n                aria-label={`Go to page ${page}`}\n                aria-current={page === currentPage ? 'page' : undefined}\n              >\n                {page}\n              </button>\n            )}\n          </div>\n        ))}\n      </div>\n\n      {/* Next Page Button */}\n      <button\n        onClick={() => onPageChange(currentPage + 1)}\n        disabled={currentPage >= totalPages}\n        className={buttonClass(false, currentPage >= totalPages)}\n        aria-label=\"Go to next page\"\n      >\n        <span className=\"hidden sm:inline mr-1\">Next</span>\n        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n        </svg>\n      </button>\n\n      {/* Last Page Button */}\n      {showFirstLast && currentPage < totalPages && (\n        <button\n          onClick={() => onPageChange(totalPages)}\n          className={buttonClass(false, false)}\n          aria-label=\"Go to last page\"\n        >\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 5l7 7-7 7M5 5l7 7-7 7\" />\n          </svg>\n        </button>\n      )}\n    </nav>\n  )\n}\n\ninterface PaginationInfoProps {\n  currentPage: number\n  totalPages: number\n  totalItems: number\n  itemsPerPage: number\n  className?: string\n}\n\nexport function PaginationInfo({\n  currentPage,\n  totalItems,\n  itemsPerPage,\n  className\n}: PaginationInfoProps) {\n  const startItem = (currentPage - 1) * itemsPerPage + 1\n  const endItem = Math.min(currentPage * itemsPerPage, totalItems)\n\n  return (\n    <div className={cn(\n      \"flex items-center justify-center text-sm text-muted-foreground\",\n      className\n    )}>\n      Showing {startItem} to {endItem} of {totalItems} posts\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAaO,SAAS,WAAW,EACzB,WAAW,EACX,UAAU,EACV,YAAY,EACZ,SAAS,EACT,gBAAgB,IAAI,EACpB,kBAAkB,CAAC,EACH;IAChB,IAAI,cAAc,GAAG,OAAO;IAE5B,MAAM,kBAAkB;QACtB,MAAM,QAA6B,EAAE;QAErC,IAAI,cAAc,iBAAiB;YACjC,mDAAmD;YACnD,IAAK,IAAI,IAAI,GAAG,KAAK,YAAY,IAAK;gBACpC,MAAM,IAAI,CAAC;YACb;QACF,OAAO;YACL,2CAA2C;YAC3C,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,cAAc,KAAK,KAAK,CAAC,kBAAkB;YACnE,MAAM,MAAM,KAAK,GAAG,CAAC,YAAY,QAAQ,kBAAkB;YAE3D,qCAAqC;YACrC,IAAI,MAAM,QAAQ,IAAI,iBAAiB;gBACrC,QAAQ,KAAK,GAAG,CAAC,GAAG,MAAM,kBAAkB;YAC9C;YAEA,wCAAwC;YACxC,IAAI,QAAQ,GAAG;gBACb,MAAM,IAAI,CAAC;gBACX,IAAI,QAAQ,GAAG;oBACb,MAAM,IAAI,CAAC;gBACb;YACF;YAEA,oBAAoB;YACpB,IAAK,IAAI,IAAI,OAAO,KAAK,KAAK,IAAK;gBACjC,MAAM,IAAI,CAAC;YACb;YAEA,uCAAuC;YACvC,IAAI,MAAM,YAAY;gBACpB,IAAI,MAAM,aAAa,GAAG;oBACxB,MAAM,IAAI,CAAC;gBACb;gBACA,MAAM,IAAI,CAAC;YACb;QACF;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;IAErB,MAAM,cAAc,CAAC,WAAoB,KAAK,EAAE,aAAsB,KAAK,GACzE,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACC,oCACA,0BACA,uBACA,mCACA,+BACA,yDACA,WACI,gEACA,aACA,sDACA;IAGR,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mDAAmD;QACjE,cAAW;;YAGV,iBAAiB,cAAc,mBAC9B,8OAAC;gBACC,SAAS,IAAM,aAAa;gBAC5B,WAAW,YAAY,OAAO;gBAC9B,cAAW;0BAEX,cAAA,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;0BAM3E,8OAAC;gBACC,SAAS,IAAM,aAAa,cAAc;gBAC1C,UAAU,eAAe;gBACzB,WAAW,YAAY,OAAO,eAAe;gBAC7C,cAAW;;kCAEX,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;kCAEvE,8OAAC;wBAAK,WAAU;kCAAwB;;;;;;;;;;;;0BAI1C,8OAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC;kCACE,SAAS,sBACR,8OAAC;4BAAK,WAAU;sCAA2E;;;;;iDAI3F,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAW,YAAY,SAAS,aAAa;4BAC7C,cAAY,CAAC,WAAW,EAAE,MAAM;4BAChC,gBAAc,SAAS,cAAc,SAAS;sCAE7C;;;;;;uBAZG;;;;;;;;;;0BAoBd,8OAAC;gBACC,SAAS,IAAM,aAAa,cAAc;gBAC1C,UAAU,eAAe;gBACzB,WAAW,YAAY,OAAO,eAAe;gBAC7C,cAAW;;kCAEX,8OAAC;wBAAK,WAAU;kCAAwB;;;;;;kCACxC,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;YAKxE,iBAAiB,cAAc,4BAC9B,8OAAC;gBACC,SAAS,IAAM,aAAa;gBAC5B,WAAW,YAAY,OAAO;gBAC9B,cAAW;0BAEX,cAAA,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;AAMjF;AAUO,SAAS,eAAe,EAC7B,WAAW,EACX,UAAU,EACV,YAAY,EACZ,SAAS,EACW;IACpB,MAAM,YAAY,CAAC,cAAc,CAAC,IAAI,eAAe;IACrD,MAAM,UAAU,KAAK,GAAG,CAAC,cAAc,cAAc;IAErD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,kEACA;;YACC;YACQ;YAAU;YAAK;YAAQ;YAAK;YAAW;;;;;;;AAGtD", "debugId": null}}, {"offset": {"line": 1090, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport { Moon, Sun } from 'lucide-react'\nimport { useTheme } from 'next-themes'\n\nexport function ThemeToggle() {\n  const { theme, setTheme } = useTheme()\n  const [mounted, setMounted] = React.useState(false)\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    return (\n      <button className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\">\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n        <span className=\"sr-only\">切換主題</span>\n      </button>\n    )\n  }\n\n  return (\n    <button\n      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}\n      className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\"\n    >\n      {theme === 'light' ? (\n        <Moon className=\"h-[1.2rem] w-[1.2rem]\" />\n      ) : (\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n      )}\n      <span className=\"sr-only\">切換主題</span>\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAO,WAAU;;8BAChB,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;8BACf,8OAAC;oBAAK,WAAU;8BAAU;;;;;;;;;;;;IAGhC;IAEA,qBACE,8OAAC;QACC,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;QACrD,WAAU;;YAET,UAAU,wBACT,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;qCAEhB,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;0BAEjB,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 1173, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/client-theme-toggle.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { ThemeToggle } from './theme-toggle'\r\n\r\nexport function ClientThemeToggle() {\r\n  return <ThemeToggle />\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS;IACd,qBAAO,8OAAC,qIAAA,CAAA,cAAW;;;;;AACrB", "debugId": null}}, {"offset": {"line": 1194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/calendar-display.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React, { useState, useEffect } from 'react'\r\n\r\nexport function CalendarDisplay() {\r\n  const [currentDateTime, setCurrentDateTime] = useState<Date | null>(null)\r\n\r\n  useEffect(() => {\r\n    setCurrentDateTime(new Date())\r\n    const timer = setInterval(() => {\r\n      setCurrentDateTime(new Date())\r\n    }, 1000)\r\n\r\n    return () => clearInterval(timer)\r\n  }, [])\r\n\r\n  const formatDate = (date: Date) => {\r\n    return date.toLocaleDateString('en-US', {\r\n      weekday: 'long',\r\n      year: 'numeric',\r\n      month: 'long',\r\n      day: 'numeric',\r\n    })\r\n  }\r\n\r\n  const formatTime = (date: Date) => {\r\n    return date.toLocaleTimeString('en-US', {\r\n      hour: '2-digit',\r\n      minute: '2-digit',\r\n      second: '2-digit',\r\n      hour12: true,\r\n    })\r\n  }\r\n\r\n  return (\r\n    <section className=\"mb-8\">\r\n      <div className=\"bg-card border border-primary/20 rounded-lg p-6 shadow-xl text-center\">\r\n        <h2 className=\"text-xl font-bold text-foreground mb-4 flex items-center justify-center\">\r\n          <svg className=\"w-6 h-6 mr-2 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\r\n          </svg>\r\n          Today's Date & Time\r\n        </h2>\r\n        {currentDateTime ? (\r\n          <>\r\n            <p className=\"text-2xl font-semibold text-foreground mb-2\">\r\n              {formatDate(currentDateTime)}\r\n            </p>\r\n            <p className=\"text-3xl font-extrabold text-primary\">\r\n              {formatTime(currentDateTime)}\r\n            </p>\r\n          </>\r\n        ) : (\r\n          <>\r\n            <p className=\"text-2xl font-semibold text-foreground mb-2 animate-pulse bg-muted h-8 w-64 mx-auto rounded\"></p>\r\n            <p className=\"text-3xl font-extrabold text-primary animate-pulse bg-muted h-10 w-40 mx-auto rounded\"></p>\r\n          </>\r\n        )}\r\n      </div>\r\n    </section>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS;IACd,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAEpE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mBAAmB,IAAI;QACvB,MAAM,QAAQ,YAAY;YACxB,mBAAmB,IAAI;QACzB,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,SAAS;YACT,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC;4BAAI,WAAU;4BAA4B,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACnF,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;wBACjE;;;;;;;gBAGP,gCACC;;sCACE,8OAAC;4BAAE,WAAU;sCACV,WAAW;;;;;;sCAEd,8OAAC;4BAAE,WAAU;sCACV,WAAW;;;;;;;iDAIhB;;sCACE,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;;;AAMzB", "debugId": null}}, {"offset": {"line": 1317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/homepage-client.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useMemo } from 'react'\nimport Link from 'next/link'\nimport { Post } from '@/types/database'\nimport { PostCard } from '@/components/post-card'\nimport { SearchBar, SearchResults } from '@/components/search-bar'\nimport { Pagination, PaginationInfo } from '@/components/pagination'\nimport { searchPosts, paginateItems, formatDate } from '@/lib/utils'\nimport { ClientThemeToggle } from '@/components/client-theme-toggle'\nimport { CalendarDisplay } from '@/components/calendar-display'\n\ninterface HomePageClientProps {\n  initialPosts: Post[]\n  user: { id: string; email?: string } | null\n  userIsAdmin: boolean\n}\n\nconst POSTS_PER_PAGE = 6\n\nexport function HomePageClient({ initialPosts: posts, user, userIsAdmin }: HomePageClientProps) {\n  const [searchQuery, setSearchQuery] = useState('')\n  const [currentPage, setCurrentPage] = useState(1)\n\n  // Filter posts based on search query and category\n  const filteredPosts = useMemo(() => {\n    return searchPosts(posts, searchQuery);\n  }, [posts, searchQuery])\n\n  // Paginate filtered posts\n  const paginatedData = useMemo(() => {\n    return paginateItems(filteredPosts, currentPage, POSTS_PER_PAGE);\n  }, [filteredPosts, currentPage])\n\n  // Reset to first page when search or category changes\n  const handleSearch = (query: string) => {\n    setSearchQuery(query)\n    setCurrentPage(1)\n  }\n\n\n  // Get featured post (most recent post)\n  const featuredPost = posts.length > 0 ? posts[0] : null\n\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"bg-card/95 shadow-lg border-b border-border sticky top-0 z-50 backdrop-blur-sm\">\n        <div className=\"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-3 sm:py-4 lg:py-6\">\n          <div className=\"flex justify-between items-center\">\n            <Link href=\"/\" className=\"text-xl sm:text-2xl lg:text-3xl font-bold text-foreground hover:text-primary transition-colors\">\n              My Blog\n            </Link>\n            <div className=\"flex items-center gap-1 sm:gap-2 lg:gap-4\">\n              <ClientThemeToggle />\n              {user ? (\n                <>\n                  <span className=\"hidden lg:block text-sm text-muted-foreground truncate max-w-32\">\n                    Welcome, {user.email}\n                  </span>\n                  \n                  {userIsAdmin && (\n                    <Link\n                      href=\"/admin/new-post\"\n                      className=\"bg-primary text-primary-foreground px-2 sm:px-3 lg:px-4 py-2 rounded-md hover:bg-primary/90 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md touch-manipulation min-h-[44px] flex items-center justify-center\"\n                    >\n                      <span className=\"hidden sm:inline\">New Post</span>\n                      <span className=\"sm:hidden text-lg\">+</span>\n                    </Link>\n                  )}\n                  {userIsAdmin && (\n                    <Link\n                      href=\"/admin/manage-posts\"\n                      className=\"bg-secondary text-secondary-foreground px-2 sm:px-3 lg:px-4 py-2 rounded-md hover:bg-secondary/80 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md touch-manipulation min-h-[44px] flex items-center justify-center\"\n                    >\n                      <span className=\"hidden sm:inline\">Manage</span>\n                      <span className=\"sm:hidden text-lg\">⚙️</span>\n                    </Link>\n                  )}\n                  <form action=\"/auth/signout\" method=\"post\">\n                    <button\n                      type=\"submit\"\n                      className=\"inline-flex items-center justify-center px-2 sm:px-3 py-2 rounded-md text-sm font-medium transition-colors bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-sm hover:shadow-md touch-manipulation min-h-[44px]\"\n                    >\n                      <svg className=\"w-4 h-4 sm:mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n                      </svg>\n                      <span className=\"hidden sm:inline\">Sign Out</span>\n                    </button>\n                  </form>\n                </>\n              ) : (\n                <Link\n                  href=\"/login\"\n                  className=\"bg-primary text-primary-foreground px-3 sm:px-4 py-2 rounded-md hover:bg-primary/90 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md touch-manipulation min-h-[44px] flex items-center justify-center\"\n                >\n                  Sign In\n                </Link>\n              )}\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-8 sm:py-12 lg:py-16\">\n        {posts.length === 0 ? (\n          /* Empty State */\n          <div className=\"text-center py-16 lg:py-24\">\n            <div className=\"max-w-md mx-auto\">\n              <div className=\"w-16 h-16 mx-auto mb-6 bg-muted rounded-full flex items-center justify-center\">\n                <svg className=\"w-8 h-8 text-muted-foreground\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n              </div>\n              <h2 className=\"text-2xl font-semibold text-foreground mb-2\">No posts yet</h2>\n              <p className=\"text-muted-foreground mb-6\">Start sharing your thoughts with the world!</p>\n              {userIsAdmin && (\n                <Link\n                  href=\"/admin/new-post\"\n                  className=\"inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-all duration-200 font-medium shadow-sm hover:shadow-md\"\n                >\n                  <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\n                  </svg>\n                  Create your first post\n                </Link>\n              )}\n            </div>\n          </div>\n        ) : (\n          <>\n            {/* Main Content Layout with Sidebar */}\n            <div className=\"flex flex-col lg:flex-row gap-8 lg:gap-12\">\n              {/* Main Content Area */}\n              <div className=\"flex-1 lg:w-2/3 order-2 lg:order-1\">\n                {/* Search and Filter Section */}\n                <section id=\"posts\" className=\"mb-8 sm:mb-12\">\n                  <div className=\"flex flex-col gap-4 sm:gap-6 mb-6 sm:mb-8\">\n                    <div className=\"flex flex-col sm:flex-row justify-between items-center gap-4 sm:gap-6\">\n                      <div className=\"text-center sm:text-left\">\n                        <h2 className=\"text-3xl sm:text-4xl font-extrabold text-foreground mb-2\">All Posts</h2>\n                        <p className=\"text-lg text-muted-foreground\">\n                          {posts.length} post{posts.length === 1 ? '' : 's'} available\n                        </p>\n                      </div>\n                      <div className=\"w-full sm:max-w-xs\">\n                        <SearchBar onSearch={handleSearch} className=\"text-sm\" />\n                      </div>\n                    </div>\n                  </div>\n\n\n                  {/* Search Results Info */}\n                  <SearchResults query={searchQuery} totalResults={filteredPosts.length} />\n                </section>\n\n                {/* Posts Grid */}\n                <section className=\"mb-16\">\n                  {paginatedData.items.length === 0 ? (\n                    <div className=\"text-center py-20\">\n                      <div className=\"w-20 h-20 mx-auto mb-8 bg-gradient-to-br from-muted to-muted/50 rounded-2xl flex items-center justify-center\">\n                        <svg className=\"w-10 h-10 text-muted-foreground\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                        </svg>\n                      </div>\n                      <h3 className=\"text-2xl font-semibold text-foreground mb-3\">No posts found</h3>\n                      <p className=\"text-muted-foreground text-lg mb-6\">Try adjusting your search terms or browse all posts</p>\n                      <Link\n                        href=\"#posts\"\n                        onClick={() => {\n                          setSearchQuery('')\n                          setCurrentPage(1)\n                        }}\n                        className=\"inline-flex items-center px-6 py-3 rounded-lg bg-primary text-primary-foreground font-medium hover:bg-primary/90 transition-colors\"\n                      >\n                        Clear Search\n                      </Link>\n                    </div>\n                  ) : (\n                    <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2\">\n                      {paginatedData.items\n                        .map((post, index) => (\n                        <PostCard\n                          key={post.id}\n                          post={post}\n                          className=\"animate-slide-up hover:scale-[1.02] transition-transform duration-300\"\n                          style={{\n                            animationDelay: `${index * 0.05}s`\n                          } as React.CSSProperties}\n                        />\n                      ))}\n                    </div>\n                  )}\n                </section>\n\n                {/* Pagination */}\n                {paginatedData.totalPages > 1 && (\n                  <section className=\"mb-12 space-y-6\">\n                    <Pagination\n                      currentPage={paginatedData.currentPage}\n                      totalPages={paginatedData.totalPages}\n                      onPageChange={setCurrentPage}\n                    />\n                    <PaginationInfo\n                      currentPage={paginatedData.currentPage}\n                      totalPages={paginatedData.totalPages}\n                      totalItems={paginatedData.totalItems}\n                      itemsPerPage={POSTS_PER_PAGE}\n                    />\n                  </section>\n                )}\n              </div>\n\n              {/* Right Sidebar */}\n              <aside className=\"lg:w-1/3 lg:max-w-sm order-1 lg:order-2\">\n                {/* Featured Post Section */}\n                <CalendarDisplay />\n\n                {/* Additional Sidebar Content - Recent Posts */}\n                {posts.length > 1 && (\n                  <section className=\"mb-8\">\n                    <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n                      <h3 className=\"text-lg font-semibold text-foreground mb-4 flex items-center\">\n                        <svg className=\"w-5 h-5 mr-2 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                        </svg>\n                        Recent Posts\n                      </h3>\n                      <div className=\"space-y-4\">\n                        {posts\n                          .filter(post => post.id !== featuredPost?.id)\n                          .slice(0, 3)\n                          .map((post) => (\n                          <div key={post.id} className=\"group\">\n                            <Link href={`/posts/${post.id}`} className=\"block\">\n                              <h4 className=\"text-sm font-medium text-foreground group-hover:text-primary transition-colors line-clamp-2 mb-1\">\n                                {post.title}\n                              </h4>\n                              <p className=\"text-xs text-muted-foreground\">\n                                {formatDate(post.created_at)}\n                              </p>\n                            </Link>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                  </section>\n                )}\n              </aside>\n            </div>\n          </>\n        )}\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;AAkBA,MAAM,iBAAiB;AAEhB,SAAS,eAAe,EAAE,cAAc,KAAK,EAAE,IAAI,EAAE,WAAW,EAAuB;IAC5F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,kDAAkD;IAClD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,OAAO,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,OAAO;IAC5B,GAAG;QAAC;QAAO;KAAY;IAEvB,0BAA0B;IAC1B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,OAAO,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,eAAe,aAAa;IACnD,GAAG;QAAC;QAAe;KAAY;IAE/B,sDAAsD;IACtD,MAAM,eAAe,CAAC;QACpB,eAAe;QACf,eAAe;IACjB;IAGA,uCAAuC;IACvC,MAAM,eAAe,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,EAAE,GAAG;IAGnD,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAiG;;;;;;0CAG1H,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,+IAAA,CAAA,oBAAiB;;;;;oCACjB,qBACC;;0DACE,8OAAC;gDAAK,WAAU;;oDAAkE;oDACtE,KAAK,KAAK;;;;;;;4CAGrB,6BACC,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,8OAAC;wDAAK,WAAU;kEAAmB;;;;;;kEACnC,8OAAC;wDAAK,WAAU;kEAAoB;;;;;;;;;;;;4CAGvC,6BACC,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,8OAAC;wDAAK,WAAU;kEAAmB;;;;;;kEACnC,8OAAC;wDAAK,WAAU;kEAAoB;;;;;;;;;;;;0DAGxC,8OAAC;gDAAK,QAAO;gDAAgB,QAAO;0DAClC,cAAA,8OAAC;oDACC,MAAK;oDACL,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;4DAAkB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACzE,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;sEAEvE,8OAAC;4DAAK,WAAU;sEAAmB;;;;;;;;;;;;;;;;;;qEAKzC,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,8OAAC;gBAAK,WAAU;0BACb,MAAM,MAAM,KAAK,IAChB,eAAe,iBACf,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAgC,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACvF,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAG,WAAU;0CAA8C;;;;;;0CAC5D,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;4BACzC,6BACC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACtE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;oCACjE;;;;;;;;;;;;;;;;;yCAOd;8BAEE,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAQ,IAAG;wCAAQ,WAAU;;0DAC5B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAA2D;;;;;;8EACzE,8OAAC;oEAAE,WAAU;;wEACV,MAAM,MAAM;wEAAC;wEAAM,MAAM,MAAM,KAAK,IAAI,KAAK;wEAAI;;;;;;;;;;;;;sEAGtD,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,mIAAA,CAAA,YAAS;gEAAC,UAAU;gEAAc,WAAU;;;;;;;;;;;;;;;;;;;;;;0DAOnD,8OAAC,mIAAA,CAAA,gBAAa;gDAAC,OAAO;gDAAa,cAAc,cAAc,MAAM;;;;;;;;;;;;kDAIvE,8OAAC;wCAAQ,WAAU;kDAChB,cAAc,KAAK,CAAC,MAAM,KAAK,kBAC9B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAkC,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACzF,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;oDAAG,WAAU;8DAA8C;;;;;;8DAC5D,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAClD,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,SAAS;wDACP,eAAe;wDACf,eAAe;oDACjB;oDACA,WAAU;8DACX;;;;;;;;;;;iEAKH,8OAAC;4CAAI,WAAU;sDACZ,cAAc,KAAK,CACjB,GAAG,CAAC,CAAC,MAAM,sBACZ,8OAAC,kIAAA,CAAA,WAAQ;oDAEP,MAAM;oDACN,WAAU;oDACV,OAAO;wDACL,gBAAgB,GAAG,QAAQ,KAAK,CAAC,CAAC;oDACpC;mDALK,KAAK,EAAE;;;;;;;;;;;;;;;oCAarB,cAAc,UAAU,GAAG,mBAC1B,8OAAC;wCAAQ,WAAU;;0DACjB,8OAAC,gIAAA,CAAA,aAAU;gDACT,aAAa,cAAc,WAAW;gDACtC,YAAY,cAAc,UAAU;gDACpC,cAAc;;;;;;0DAEhB,8OAAC,gIAAA,CAAA,iBAAc;gDACb,aAAa,cAAc,WAAW;gDACtC,YAAY,cAAc,UAAU;gDACpC,YAAY,cAAc,UAAU;gDACpC,cAAc;;;;;;;;;;;;;;;;;;0CAOtB,8OAAC;gCAAM,WAAU;;kDAEf,8OAAC,yIAAA,CAAA,kBAAe;;;;;oCAGf,MAAM,MAAM,GAAG,mBACd,8OAAC;wCAAQ,WAAU;kDACjB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAI,WAAU;4DAA4B,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACnF,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;wDACjE;;;;;;;8DAGR,8OAAC;oDAAI,WAAU;8DACZ,MACE,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,cAAc,IACzC,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,qBACN,8OAAC;4DAAkB,WAAU;sEAC3B,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;gEAAE,WAAU;;kFACzC,8OAAC;wEAAG,WAAU;kFACX,KAAK,KAAK;;;;;;kFAEb,8OAAC;wEAAE,WAAU;kFACV,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAAU;;;;;;;;;;;;2DANvB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsB3C", "debugId": null}}, {"offset": {"line": 1959, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/skeleton.tsx"], "sourcesContent": ["'use client'\n\nimport { cn } from '@/lib/utils'\n\ninterface SkeletonProps {\n  className?: string\n}\n\nexport function Skeleton({ className }: SkeletonProps) {\n  return (\n    <div\n      className={cn(\n        \"animate-pulse rounded-md bg-muted\",\n        className\n      )}\n    />\n  )\n}\n\nexport function PostCardSkeleton({ className }: { className?: string }) {\n  return (\n    <div className={cn(\n      \"bg-card rounded-xl shadow-sm border border-border p-6 lg:p-8\",\n      className\n    )}>\n      <div className=\"space-y-4\">\n        {/* Title skeleton */}\n        <Skeleton className=\"h-7 w-3/4\" />\n        \n        {/* Content skeleton - multiple lines */}\n        <div className=\"space-y-2\">\n          <Skeleton className=\"h-4 w-full\" />\n          <Skeleton className=\"h-4 w-full\" />\n          <Skeleton className=\"h-4 w-2/3\" />\n        </div>\n        \n        {/* Footer skeleton */}\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 pt-2\">\n          <div className=\"flex items-center gap-2\">\n            <Skeleton className=\"h-4 w-4\" />\n            <Skeleton className=\"h-4 w-32\" />\n          </div>\n          <Skeleton className=\"h-4 w-20\" />\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport function PostGridSkeleton({ count = 6 }: { count?: number }) {\n  return (\n    <div className=\"grid gap-6 lg:gap-8 md:grid-cols-2 lg:grid-cols-3\">\n      {Array.from({ length: count }).map((_, index) => (\n        <PostCardSkeleton key={index} />\n      ))}\n    </div>\n  )\n}\n\nexport function HeroSkeleton() {\n  return (\n    <div className=\"text-center py-16 lg:py-24 space-y-6\">\n      <div className=\"space-y-4\">\n        <Skeleton className=\"h-12 w-2/3 mx-auto\" />\n        <Skeleton className=\"h-6 w-1/2 mx-auto\" />\n      </div>\n      <div className=\"space-y-3\">\n        <Skeleton className=\"h-4 w-3/4 mx-auto\" />\n        <Skeleton className=\"h-4 w-2/3 mx-auto\" />\n      </div>\n      <Skeleton className=\"h-12 w-40 mx-auto\" />\n    </div>\n  )\n}\n\nexport function SearchBarSkeleton() {\n  return (\n    <div className=\"relative\">\n      <Skeleton className=\"h-12 w-full rounded-lg\" />\n    </div>\n  )\n}\n\nexport function FeaturedPostSkeleton() {\n  return (\n    <div className=\"bg-gradient-to-r from-primary/5 to-primary/10 rounded-2xl p-6 lg:p-8 border border-primary/20\">\n      <div className=\"space-y-4\">\n        {/* Featured badge */}\n        <Skeleton className=\"h-6 w-20\" />\n        \n        {/* Title */}\n        <Skeleton className=\"h-8 w-4/5\" />\n        \n        {/* Content */}\n        <div className=\"space-y-2\">\n          <Skeleton className=\"h-4 w-full\" />\n          <Skeleton className=\"h-4 w-full\" />\n          <Skeleton className=\"h-4 w-3/4\" />\n        </div>\n        \n        {/* Footer */}\n        <div className=\"flex items-center justify-between pt-2\">\n          <div className=\"flex items-center gap-2\">\n            <Skeleton className=\"h-4 w-4\" />\n            <Skeleton className=\"h-4 w-32\" />\n          </div>\n          <Skeleton className=\"h-4 w-24\" />\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport function FilterSkeleton() {\n  return (\n    <div className=\"flex flex-wrap gap-2\">\n      {Array.from({ length: 4 }).map((_, index) => (\n        <Skeleton key={index} className=\"h-8 w-20 rounded-full\" />\n      ))}\n    </div>\n  )\n}\n\nexport function PaginationSkeleton() {\n  return (\n    <div className=\"flex items-center justify-center gap-2\">\n      <Skeleton className=\"h-10 w-20\" />\n      {Array.from({ length: 5 }).map((_, index) => (\n        <Skeleton key={index} className=\"h-10 w-10\" />\n      ))}\n      <Skeleton className=\"h-10 w-20\" />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAFA;;;AAQO,SAAS,SAAS,EAAE,SAAS,EAAiB;IACnD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qCACA;;;;;;AAIR;AAEO,SAAS,iBAAiB,EAAE,SAAS,EAA0B;IACpE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,gEACA;kBAEA,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAS,WAAU;;;;;;8BAGpB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAS,WAAU;;;;;;sCACpB,8OAAC;4BAAS,WAAU;;;;;;sCACpB,8OAAC;4BAAS,WAAU;;;;;;;;;;;;8BAItB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAS,WAAU;;;;;;8CACpB,8OAAC;oCAAS,WAAU;;;;;;;;;;;;sCAEtB,8OAAC;4BAAS,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK9B;AAEO,SAAS,iBAAiB,EAAE,QAAQ,CAAC,EAAsB;IAChE,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,8OAAC,sBAAsB;;;;;;;;;;AAI/B;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAS,WAAU;;;;;;kCACpB,8OAAC;wBAAS,WAAU;;;;;;;;;;;;0BAEtB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAS,WAAU;;;;;;kCACpB,8OAAC;wBAAS,WAAU;;;;;;;;;;;;0BAEtB,8OAAC;gBAAS,WAAU;;;;;;;;;;;;AAG1B;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAS,WAAU;;;;;;;;;;;AAG1B;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAS,WAAU;;;;;;8BAGpB,8OAAC;oBAAS,WAAU;;;;;;8BAGpB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAS,WAAU;;;;;;sCACpB,8OAAC;4BAAS,WAAU;;;;;;sCACpB,8OAAC;4BAAS,WAAU;;;;;;;;;;;;8BAItB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAS,WAAU;;;;;;8CACpB,8OAAC;oCAAS,WAAU;;;;;;;;;;;;sCAEtB,8OAAC;4BAAS,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK9B;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;gBAAqB,WAAU;eAAjB;;;;;;;;;;AAIvB;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAS,WAAU;;;;;;YACnB,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;oBAAqB,WAAU;mBAAjB;;;;;0BAEjB,8OAAC;gBAAS,WAAU;;;;;;;;;;;;AAG1B", "debugId": null}}]}