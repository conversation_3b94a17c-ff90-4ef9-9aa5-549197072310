{"version": 3, "sources": [], "sections": [{"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\r\n\r\nexport function createClient() {\r\n  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'\r\n  const hostname = new URL(siteUrl).hostname\r\n\r\n  // Fix localhost cookie domain issue\r\n  const cookieDomain = hostname === 'localhost' ? undefined : hostname\r\n\r\n  return createBrowserClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookieOptions: {\r\n        domain: cookieDomain,\r\n        sameSite: 'lax',\r\n        secure: hostname !== 'localhost'\r\n      },\r\n      auth: {\r\n        autoRefreshToken: true,\r\n        persistSession: true,\r\n        detectSessionInUrl: true\r\n      }\r\n    }\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,SAAS;IACd,MAAM,UAAU,0EAAoC;IACpD,MAAM,WAAW,IAAI,IAAI,SAAS,QAAQ;IAE1C,oCAAoC;IACpC,MAAM,eAAe,aAAa,cAAc,YAAY;IAE5D,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,sUAGvB;QACE,eAAe;YACb,QAAQ;YACR,UAAU;YACV,QAAQ,aAAa;QACvB;QACA,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;YAChB,oBAAoB;QACtB;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport { Moon, Sun } from 'lucide-react'\nimport { useTheme } from 'next-themes'\n\nexport function ThemeToggle() {\n  const { theme, setTheme } = useTheme()\n  const [mounted, setMounted] = React.useState(false)\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    return (\n      <button className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\">\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n        <span className=\"sr-only\">切換主題</span>\n      </button>\n    )\n  }\n\n  return (\n    <button\n      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}\n      className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\"\n    >\n      {theme === 'light' ? (\n        <Moon className=\"h-[1.2rem] w-[1.2rem]\" />\n      ) : (\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n      )}\n      <span className=\"sr-only\">切換主題</span>\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAO,WAAU;;8BAChB,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;8BACf,8OAAC;oBAAK,WAAU;8BAAU;;;;;;;;;;;;IAGhC;IAEA,qBACE,8OAAC;QACC,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;QACrD,WAAU;;YAET,UAAU,wBACT,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;qCAEhB,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;0BAEjB,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/client-theme-toggle.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { ThemeToggle } from './theme-toggle'\r\n\r\nexport function ClientThemeToggle() {\r\n  return <ThemeToggle />\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS;IACd,qBAAO,8OAAC,qIAAA,CAAA,cAAW;;;;;AACrB", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/app/login/page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { createClient } from '@/lib/supabase/client'\r\nimport { useRouter } from 'next/navigation'\r\nimport Link from 'next/link'\r\nimport { ClientThemeToggle } from '@/components/client-theme-toggle'\r\n\r\nexport default function Login() {\r\n  const [email, setEmail] = useState('')\r\n  const [password, setPassword] = useState('')\r\n  const [loading, setLoading] = useState(false)\r\n  const [message, setMessage] = useState('')\r\n  const [isSignUp, setIsSignUp] = useState(false)\r\n  const router = useRouter()\r\n  const supabase = createClient()\r\n\r\n  const handleAuth = async (e: React.FormEvent) => {\r\n    e.preventDefault()\r\n    setLoading(true)\r\n    setMessage('')\r\n\r\n    try {\r\n      if (isSignUp) {\r\n        const { error } = await supabase.auth.signUp({\r\n          email,\r\n          password,\r\n        })\r\n        if (error) throw error\r\n        setMessage('Registration successful! Please check your email to verify your account.')\r\n      } else {\r\n        const { error } = await supabase.auth.signInWithPassword({\r\n          email,\r\n          password,\r\n        })\r\n        if (error) throw error\r\n        router.push('/')\r\n        router.refresh()\r\n      }\r\n    } catch (error) {\r\n      setMessage(error instanceof Error ? error.message : 'An error occurred')\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-background\">\r\n      {/* Header */}\r\n      <header className=\"bg-card/95 shadow-sm border-b border-border sticky top-0 z-50 backdrop-blur-sm\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 lg:py-6\">\r\n          <div className=\"flex justify-between items-center\">\r\n            <Link\r\n              href=\"/\"\r\n              className=\"text-xl sm:text-2xl font-bold text-foreground hover:text-primary transition-colors\"\r\n            >\r\n              My Blog\r\n            </Link>\r\n            <ClientThemeToggle />\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      {/* Main Content */}\r\n      <div className=\"flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"max-w-md w-full space-y-8\">\r\n          <div className=\"text-center\">\r\n            <div className=\"w-16 h-16 mx-auto mb-6 bg-primary/10 rounded-full flex items-center justify-center\">\r\n              <svg className=\"w-8 h-8 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\r\n              </svg>\r\n            </div>\r\n            <h2 className=\"text-2xl sm:text-3xl font-bold text-foreground mb-2\">\r\n              {isSignUp ? 'Create your account' : 'Welcome back'}\r\n            </h2>\r\n            <p className=\"text-muted-foreground\">\r\n              {isSignUp ? 'Join our community today' : 'Sign in to your account'}\r\n            </p>\r\n            <p className=\"mt-4 text-sm text-muted-foreground\">\r\n              Or{' '}\r\n              <Link href=\"/\" className=\"font-medium text-primary hover:text-primary/80 transition-colors\">\r\n                return to home\r\n              </Link>\r\n            </p>\r\n          </div>\r\n          <div className=\"bg-card rounded-xl shadow-lg border border-border p-6 sm:p-8\">\r\n            <form className=\"space-y-6\" onSubmit={handleAuth}>\r\n              <div className=\"space-y-5\">\r\n                <div>\r\n                  <label htmlFor=\"email\" className=\"block text-sm font-medium text-foreground mb-2\">\r\n                    Email address\r\n                  </label>\r\n                  <input\r\n                    id=\"email\"\r\n                    name=\"email\"\r\n                    type=\"email\"\r\n                    autoComplete=\"email\"\r\n                    required\r\n                    className=\"w-full px-4 py-3 border border-input bg-background text-foreground rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 placeholder:text-muted-foreground\"\r\n                    placeholder=\"Enter your email address\"\r\n                    value={email}\r\n                    onChange={(e) => setEmail(e.target.value)}\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label htmlFor=\"password\" className=\"block text-sm font-medium text-foreground mb-2\">\r\n                    Password\r\n                  </label>\r\n                  <input\r\n                    id=\"password\"\r\n                    name=\"password\"\r\n                    type=\"password\"\r\n                    autoComplete=\"current-password\"\r\n                    required\r\n                    className=\"w-full px-4 py-3 border border-input bg-background text-foreground rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 placeholder:text-muted-foreground\"\r\n                    placeholder=\"Enter your password\"\r\n                    value={password}\r\n                    onChange={(e) => setPassword(e.target.value)}\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              {message && (\r\n                <div className={`text-sm text-center p-4 rounded-lg ${\r\n                  message.includes('successful')\r\n                    ? 'bg-green-50 text-green-700 border border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800'\r\n                    : 'bg-destructive/10 text-destructive border border-destructive/20'\r\n                }`}>\r\n                  {message}\r\n                </div>\r\n              )}\r\n\r\n              <div>\r\n                <button\r\n                  type=\"submit\"\r\n                  disabled={loading}\r\n                  className=\"w-full bg-primary text-primary-foreground py-3 px-4 rounded-lg hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-sm hover:shadow-md\"\r\n                >\r\n                  {loading ? (\r\n                    <div className=\"flex items-center justify-center\">\r\n                      <div className=\"w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin mr-2\"></div>\r\n                      Processing...\r\n                    </div>\r\n                  ) : (\r\n                    isSignUp ? 'Create Account' : 'Sign In'\r\n                  )}\r\n                </button>\r\n              </div>\r\n\r\n              <div className=\"text-center\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() => setIsSignUp(!isSignUp)}\r\n                  className=\"text-primary hover:text-primary/80 text-sm transition-colors font-medium\"\r\n                  suppressHydrationWarning={true}\r\n                >\r\n                  {isSignUp ? 'Already have an account? Sign in' : \"Don't have an account? Sign up\"}\r\n                </button>\r\n              </div>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,aAAa,OAAO;QACxB,EAAE,cAAc;QAChB,WAAW;QACX,WAAW;QAEX,IAAI;YACF,IAAI,UAAU;gBACZ,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;oBAC3C;oBACA;gBACF;gBACA,IAAI,OAAO,MAAM;gBACjB,WAAW;YACb,OAAO;gBACL,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;oBACvD;oBACA;gBACF;gBACA,IAAI,OAAO,MAAM;gBACjB,OAAO,IAAI,CAAC;gBACZ,OAAO,OAAO;YAChB;QACF,EAAE,OAAO,OAAO;YACd,WAAW,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACtD,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,+IAAA,CAAA,oBAAiB;;;;;;;;;;;;;;;;;;;;;0BAMxB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAuB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC9E,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,8OAAC;oCAAG,WAAU;8CACX,WAAW,wBAAwB;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CACV,WAAW,6BAA6B;;;;;;8CAE3C,8OAAC;oCAAE,WAAU;;wCAAqC;wCAC7C;sDACH,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDAAmE;;;;;;;;;;;;;;;;;;sCAKhG,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;gCAAY,UAAU;;kDACpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAQ,WAAU;kEAAiD;;;;;;kEAGlF,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,cAAa;wDACb,QAAQ;wDACR,WAAU;wDACV,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;0DAG5C,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAW,WAAU;kEAAiD;;;;;;kEAGrF,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,cAAa;wDACb,QAAQ;wDACR,WAAU;wDACV,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;oCAKhD,yBACC,8OAAC;wCAAI,WAAW,CAAC,mCAAmC,EAClD,QAAQ,QAAQ,CAAC,gBACb,sHACA,mEACJ;kDACC;;;;;;kDAIL,8OAAC;kDACC,cAAA,8OAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,wBACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;oDAA4F;;;;;;uDAI7G,WAAW,mBAAmB;;;;;;;;;;;kDAKpC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,YAAY,CAAC;4CAC5B,WAAU;4CACV,0BAA0B;sDAEzB,WAAW,qCAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnE", "debugId": null}}]}