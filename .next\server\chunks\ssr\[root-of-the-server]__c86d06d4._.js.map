{"version": 3, "sources": [], "sections": [{"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookieOptions: {\n        domain: new URL(siteUrl).hostname,\n        sameSite: 'lax',\n        secure: false\n      },\n      auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n      }\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,SAAS;IACd,MAAM,UAAU,0EAAoC;IACpD,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,sUAGvB;QACE,eAAe;YACb,QAAQ,IAAI,IAAI,SAAS,QAAQ;YACjC,UAAU;YACV,QAAQ;QACV;QACA,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;YAChB,oBAAoB;QACtB;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport { Moon, Sun } from 'lucide-react'\nimport { useTheme } from 'next-themes'\n\nexport function ThemeToggle() {\n  const { theme, setTheme } = useTheme()\n  const [mounted, setMounted] = React.useState(false)\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    return (\n      <button className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\">\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n        <span className=\"sr-only\">切換主題</span>\n      </button>\n    )\n  }\n\n  return (\n    <button\n      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}\n      className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\"\n    >\n      {theme === 'light' ? (\n        <Moon className=\"h-[1.2rem] w-[1.2rem]\" />\n      ) : (\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n      )}\n      <span className=\"sr-only\">切換主題</span>\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAO,WAAU;;8BAChB,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;8BACf,8OAAC;oBAAK,WAAU;8BAAU;;;;;;;;;;;;IAGhC;IAEA,qBACE,8OAAC;QACC,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;QACrD,WAAU;;YAET,UAAU,wBACT,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;qCAEhB,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;0BAEjB,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/client-theme-toggle.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { ThemeToggle } from './theme-toggle'\r\n\r\nexport function ClientThemeToggle() {\r\n  return <ThemeToggle />\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS;IACd,qBAAO,8OAAC,qIAAA,CAAA,cAAW;;;;;AACrB", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/app/login/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { createClient } from '@/lib/supabase/client'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { ClientThemeToggle } from '@/components/client-theme-toggle'\n\nexport default function Login() {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [message, setMessage] = useState('')\n  const [isSignUp, setIsSignUp] = useState(false)\n  const router = useRouter()\n  const supabase = createClient()\n\n  const handleAuth = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setMessage('')\n\n    try {\n      if (isSignUp) {\n        const { error } = await supabase.auth.signUp({\n          email,\n          password,\n        })\n        if (error) throw error\n        setMessage('Registration successful! Please check your email to verify your account.')\n      } else {\n        const { error } = await supabase.auth.signInWithPassword({\n          email,\n          password,\n        })\n        if (error) throw error\n        router.push('/')\n        router.refresh()\n      }\n    } catch (error) {\n      setMessage(error instanceof Error ? error.message : 'An error occurred')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"bg-card/95 shadow-sm border-b border-border sticky top-0 z-50 backdrop-blur-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 lg:py-6\">\n          <div className=\"flex justify-between items-center\">\n            <Link\n              href=\"/\"\n              className=\"text-xl sm:text-2xl font-bold text-foreground hover:text-primary transition-colors\"\n            >\n              My Blog\n            </Link>\n            <ClientThemeToggle />\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <div className=\"flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-md w-full space-y-8\">\n          <div className=\"text-center\">\n            <div className=\"w-16 h-16 mx-auto mb-6 bg-primary/10 rounded-full flex items-center justify-center\">\n              <svg className=\"w-8 h-8 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n              </svg>\n            </div>\n            <h2 className=\"text-2xl sm:text-3xl font-bold text-foreground mb-2\">\n              {isSignUp ? 'Create your account' : 'Welcome back'}\n            </h2>\n            <p className=\"text-muted-foreground\">\n              {isSignUp ? 'Join our community today' : 'Sign in to your account'}\n            </p>\n            <p className=\"mt-4 text-sm text-muted-foreground\">\n              Or{' '}\n              <Link href=\"/\" className=\"font-medium text-primary hover:text-primary/80 transition-colors\">\n                return to home\n              </Link>\n            </p>\n          </div>\n          <div className=\"bg-card rounded-xl shadow-lg border border-border p-6 sm:p-8\">\n            <form className=\"space-y-6\" onSubmit={handleAuth}>\n              <div className=\"space-y-5\">\n                <div>\n                  <label htmlFor=\"email\" className=\"block text-sm font-medium text-foreground mb-2\">\n                    Email address\n                  </label>\n                  <input\n                    id=\"email\"\n                    name=\"email\"\n                    type=\"email\"\n                    autoComplete=\"email\"\n                    required\n                    className=\"w-full px-4 py-3 border border-input bg-background text-foreground rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 placeholder:text-muted-foreground\"\n                    placeholder=\"Enter your email address\"\n                    value={email}\n                    onChange={(e) => setEmail(e.target.value)}\n                  />\n                </div>\n                <div>\n                  <label htmlFor=\"password\" className=\"block text-sm font-medium text-foreground mb-2\">\n                    Password\n                  </label>\n                  <input\n                    id=\"password\"\n                    name=\"password\"\n                    type=\"password\"\n                    autoComplete=\"current-password\"\n                    required\n                    className=\"w-full px-4 py-3 border border-input bg-background text-foreground rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 placeholder:text-muted-foreground\"\n                    placeholder=\"Enter your password\"\n                    value={password}\n                    onChange={(e) => setPassword(e.target.value)}\n                  />\n                </div>\n              </div>\n\n              {message && (\n                <div className={`text-sm text-center p-4 rounded-lg ${\n                  message.includes('successful')\n                    ? 'bg-green-50 text-green-700 border border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800'\n                    : 'bg-destructive/10 text-destructive border border-destructive/20'\n                }`}>\n                  {message}\n                </div>\n              )}\n\n              <div>\n                <button\n                  type=\"submit\"\n                  disabled={loading}\n                  className=\"w-full bg-primary text-primary-foreground py-3 px-4 rounded-lg hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-sm hover:shadow-md\"\n                >\n                  {loading ? (\n                    <div className=\"flex items-center justify-center\">\n                      <div className=\"w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin mr-2\"></div>\n                      Processing...\n                    </div>\n                  ) : (\n                    isSignUp ? 'Create Account' : 'Sign In'\n                  )}\n                </button>\n              </div>\n\n              <div className=\"text-center\">\n                <button\n                  type=\"button\"\n                  onClick={() => setIsSignUp(!isSignUp)}\n                  className=\"text-primary hover:text-primary/80 text-sm transition-colors font-medium\"\n                  suppressHydrationWarning={true}\n                >\n                  {isSignUp ? 'Already have an account? Sign in' : \"Don't have an account? Sign up\"}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,aAAa,OAAO;QACxB,EAAE,cAAc;QAChB,WAAW;QACX,WAAW;QAEX,IAAI;YACF,IAAI,UAAU;gBACZ,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;oBAC3C;oBACA;gBACF;gBACA,IAAI,OAAO,MAAM;gBACjB,WAAW;YACb,OAAO;gBACL,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;oBACvD;oBACA;gBACF;gBACA,IAAI,OAAO,MAAM;gBACjB,OAAO,IAAI,CAAC;gBACZ,OAAO,OAAO;YAChB;QACF,EAAE,OAAO,OAAO;YACd,WAAW,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACtD,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,+IAAA,CAAA,oBAAiB;;;;;;;;;;;;;;;;;;;;;0BAMxB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAuB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC9E,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,8OAAC;oCAAG,WAAU;8CACX,WAAW,wBAAwB;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CACV,WAAW,6BAA6B;;;;;;8CAE3C,8OAAC;oCAAE,WAAU;;wCAAqC;wCAC7C;sDACH,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDAAmE;;;;;;;;;;;;;;;;;;sCAKhG,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;gCAAY,UAAU;;kDACpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAQ,WAAU;kEAAiD;;;;;;kEAGlF,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,cAAa;wDACb,QAAQ;wDACR,WAAU;wDACV,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;0DAG5C,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAW,WAAU;kEAAiD;;;;;;kEAGrF,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,cAAa;wDACb,QAAQ;wDACR,WAAU;wDACV,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;oCAKhD,yBACC,8OAAC;wCAAI,WAAW,CAAC,mCAAmC,EAClD,QAAQ,QAAQ,CAAC,gBACb,sHACA,mEACJ;kDACC;;;;;;kDAIL,8OAAC;kDACC,cAAA,8OAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,wBACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;oDAA4F;;;;;;uDAI7G,WAAW,mBAAmB;;;;;;;;;;;kDAKpC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,YAAY,CAAC;4CAC5B,WAAU;4CACV,0BAA0B;sDAEzB,WAAW,qCAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnE", "debugId": null}}]}